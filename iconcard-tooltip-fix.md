# Sửa lỗi Tooltip không hiển thị trong IconCard

## Vấn đề
- IconCard component có prop `title` nhưng tooltip không hiển thị khi hover
- Mặc dù đã truyền `title={t('rpoint.payment.backToPackages', 'Quay lại danh sách gói')}` nhưng không thấy tooltip

## Nguyên nhân
IconCard đang sử dụng component `Tooltip` cũ có vấn đề về positioning và z-index:

```tsx
// ❌ Component cũ có vấn đề
import Tooltip from '@/shared/components/common/Tooltip';

// Trong render:
<Tooltip content={title} position="top">
  {iconCardElement}
</Tooltip>
```

## Giải pháp
Thay thế bằng `ModernTooltip` component hoạt động tốt hơn:

```tsx
// ✅ Component mới hoạt động tốt
import ModernTooltip from '@/shared/components/common/ModernTooltip';

// Trong render:
<ModernTooltip content={title} position="top">
  {iconCardElement}
</ModernTooltip>
```

## Thay đổi đã thực hiện

### File: `src/shared/components/common/IconCard/IconCard.tsx`

1. **Thay đổi import:**
   ```tsx
   // Trước
   import Tooltip from '@/shared/components/common/Tooltip';
   
   // Sau
   import ModernTooltip from '@/shared/components/common/ModernTooltip';
   ```

2. **Thay đổi component sử dụng:**
   ```tsx
   // Trước
   if (title) {
     return (
       <Tooltip content={title} position="top">
         {iconCardElement}
       </Tooltip>
     );
   }
   
   // Sau
   if (title) {
     return (
       <ModernTooltip content={title} position="top">
         {iconCardElement}
       </ModernTooltip>
     );
   }
   ```

## So sánh 2 component Tooltip

### Tooltip (cũ)
- ❌ Có vấn đề về positioning
- ❌ Z-index không đúng
- ❌ Animation không mượt
- ❌ Positioning logic phức tạp và có bug

### ModernTooltip (mới)
- ✅ Positioning chính xác
- ✅ Z-index cao (z-50)
- ✅ Animation mượt mà
- ✅ Logic positioning đơn giản và ổn định
- ✅ Hỗ trợ nhiều variant (dark/light)
- ✅ Responsive tốt

## Kết quả
Bây giờ khi hover vào IconCard với prop `title`, tooltip sẽ hiển thị đúng cách:

```tsx
<IconCard
  icon="arrow-left"
  onClick={() => navigate('/rpoint/packages')}
  variant="secondary"
  size="md"
  title={t('rpoint.payment.backToPackages', 'Quay lại danh sách gói')}
/>
```

Tooltip sẽ hiển thị "Quay lại danh sách gói" khi hover vào icon.

## Files đã thay đổi
1. `src/shared/components/common/IconCard/IconCard.tsx`

## Status
✅ TypeScript compilation passed
✅ Tooltip hiển thị đúng cách
✅ Component hoạt động ổn định
✅ Không ảnh hưởng đến các component khác

## Test Cases
1. **Hover vào IconCard có title** → Tooltip hiển thị
2. **Hover vào IconCard không có title** → Không có tooltip
3. **Tooltip positioning** → Hiển thị ở vị trí đúng (top)
4. **Animation** → Fade in/out mượt mà
5. **Z-index** → Tooltip hiển thị trên các element khác
