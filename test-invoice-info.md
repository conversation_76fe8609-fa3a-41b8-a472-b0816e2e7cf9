# Test Invoice Info Component

## Thay đổi đã thực hiện

### 1. Schema Changes
- Thêm các trường thông tin cá nhân vào `personalInvoiceSchema`
- <PERSON><PERSON><PERSON> trường bắt buộc: fullName, phoneNumber, address, email
- <PERSON><PERSON><PERSON> trường tùy chọn: dateOfBirth, gender

### 2. Component Changes
- Thêm form fields cho thông tin cá nhân
- Tự động điền thông tin từ profile người dùng
- Hi<PERSON><PERSON> thị khi chọn "<PERSON><PERSON> nhân" trong loại hóa đơn

### 3. Translation Updates
- Thêm keys cho vi.json, en.json, zh.json
- <PERSON><PERSON> gồm labels và placeholders cho tất cả fields

## Test Cases

### Test 1: <PERSON><PERSON><PERSON> "Cá nhân"
1. Vào trang /rpoint/order
2. <PERSON><PERSON><PERSON> "<PERSON><PERSON> nhân" trong phần thông tin xuất hóa đơn
3. <PERSON><PERSON><PERSON> tra form hiển thị các trường:
   - <PERSON><PERSON> và tên (required)
   - <PERSON><PERSON> điện thoại (required)
   - <PERSON><PERSON> (required)
   - <PERSON><PERSON><PERSON> chỉ (required)
   - <PERSON><PERSON><PERSON> sinh (optional)
   - Giới tính (optional)

### Test 2: Auto-fill từ profile
1. Đảm bảo user đã có thông tin trong profile
2. Chọn "Cá nhân"
3. Kiểm tra thông tin được tự động điền

### Test 3: Validation
1. Submit form với các trường bắt buộc để trống
2. Kiểm tra validation messages hiển thị

### Test 4: Switch giữa Personal và Business
1. Chọn "Cá nhân" -> kiểm tra form cá nhân hiển thị
2. Chọn "Doanh nghiệp" -> kiểm tra form doanh nghiệp hiển thị
3. Chọn lại "Cá nhân" -> kiểm tra thông tin vẫn được giữ

## Files Modified

1. `src/modules/rpoint/schemas/order.schema.ts`
2. `src/modules/rpoint/components/InvoiceInfo.tsx`
3. `src/modules/rpoint/locales/vi.json`
4. `src/modules/rpoint/locales/en.json`
5. `src/modules/rpoint/locales/zh.json`

## Status
✅ Schema updated
✅ Component updated
✅ Translations added
✅ TypeScript compilation passed
✅ ESLint passed for modified files
