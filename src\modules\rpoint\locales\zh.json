{"rpoint": {"packages": {"title": "R-Point 套餐", "description": "选择适合您需求的 R-Point 套餐", "standardPackages": "标准套餐", "customPackage": "自定义套餐", "selectPackage": "选择套餐", "topUp": "立即充值", "popular": "热门", "customDescription": "输入您想充值的 R-Point 以查看相应的金额", "amount": "金额 (VND)", "minAmount": "最低 {{amount}}", "points": "R-Point", "minPoints": "最低 {{points}}"}, "order": {"title": "订单信息", "checkout": "结账", "back": "返回", "userInfo": {"title": "账户信息"}, "summary": {"title": "订单摘要", "points": "R-Point", "listPrice": "标价", "discount": "折扣", "subtotal": "小计（不含增值税）", "vat": "增值税 (10%)", "total": "总付款"}, "paymentMethod": {"title": "支付方式", "bankTransfer": "银行转账", "bankTransferDesc": "通过扫描二维码或银行转账付款", "creditCard": "信用卡/借记卡", "creditCardDesc": "使用 Visa、Mastercard、JCB 付款", "eWallet": "电子钱包", "eWalletDesc": "通过 Momo、ZaloPay、VNPay 付款", "comingSoon": "即将推出"}, "coupon": {"title": "优惠券代码", "placeholder": "输入优惠券代码", "apply": "应用", "applied": "已应用", "suggested": "可用优惠券"}, "invoice": {"title": "发票信息", "personal": "个人", "business": "企业", "representativeName": "代表姓名", "representativeNamePlaceholder": "输入代表姓名", "representativePosition": "代表职位", "representativePositionPlaceholder": "输入代表职位", "companyName": "公司名称", "companyNamePlaceholder": "输入公司名称", "taxCode": "税号", "taxCodePlaceholder": "输入税号", "companyAddress": "公司地址", "companyAddressPlaceholder": "输入公司地址", "email": "发票电子邮件", "emailPlaceholder": "输入接收发票的电子邮件", "fullName": "姓名", "fullNamePlaceholder": "输入姓名", "phoneNumber": "电话号码", "phoneNumberPlaceholder": "输入电话号码", "address": "地址", "addressPlaceholder": "输入地址", "dateOfBirth": "出生日期", "dateOfBirthPlaceholder": "选择出生日期", "gender": "性别", "genderPlaceholder": "选择性别", "gender.male": "男", "gender.female": "女", "gender.other": "其他"}}, "payment": {"title": "支付", "scanQR": "扫描二维码付款", "amount": "金额", "bankTransfer": "银行转账信息", "accountHolder": "账户持有人", "accountNumber": "账号", "transferContent": "转账内容", "orderInfo": "订单信息", "orderId": "订单编号", "package": "R-Point 套餐", "points": "R-Point", "status": "状态", "statusPending": "待处理", "statusCompleted": "已完成", "backToPackages": "返回套餐列表", "error": "发生错误", "notFound": "未找到订单信息"}}}